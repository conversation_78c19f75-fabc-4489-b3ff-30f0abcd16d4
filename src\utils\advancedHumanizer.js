/**
 * Advanced Humanization Engine - Designed to achieve ≤20% AI detection
 * Implements sophisticated algorithms based on modern AI detection research
 * Enhanced with smart content analysis and formatting preservation
 */

import {
    analyzeDocumentStructure,
    identifyProtectedSentences,
    preserveFormatting,
    calculateHesitationFrequency
} from './contentAnalyzer.js';

/**
 * Main advanced humanization function
 * Applies multiple sophisticated techniques to bypass AI detection
 */
export function advancedHumanization(text, options = {}) {
    const {
        aggressiveness = 0.7, // 0-1 scale, higher = more changes
        preserveLength = false,
        maintainTone = true
    } = options;

    const originalText = text;
    let result = text;

    console.log('Starting advanced humanization...');

    // Phase 1: Smart Content Analysis and Protection
    const documentAnalysis = analyzeDocumentStructure(result);
    const hesitationFrequency = calculateHesitationFrequency(documentAnalysis);

    // For formal documents with structure, use line-by-line processing
    if (documentAnalysis.preserveFormatting) {
        console.log('Using formatted document processing...');
        result = processFormattedDocument(originalText, aggressiveness, hesitationFrequency, documentAnalysis);
    } else {
        // For regular text, use sentence-based processing
        const protectedSentences = identifyProtectedSentences(result, documentAnalysis);
        const textAnalysis = analyzeTextStructure(result);

        // Phase 2: Advanced Sentence Restructuring (most important for detection bypass)
        result = advancedSentenceRestructuring(result, textAnalysis, aggressiveness, protectedSentences);

        // Phase 3: Context-Aware Synonym Replacement
        result = contextAwareSynonymReplacement(result, aggressiveness, protectedSentences);

        // Phase 4: Perplexity and Burstiness Enhancement
        result = enhancePerplexityAndBurstiness(result, textAnalysis, aggressiveness, protectedSentences);

        // Phase 5: Human Writing Pattern Injection (with reduced frequency)
        result = injectHumanWritingPatterns(result, aggressiveness, hesitationFrequency, protectedSentences);

        // Phase 6: Semantic Coherence Disruption (subtle)
        result = subtleCoherenceDisruption(result, aggressiveness, protectedSentences);
    }

    // Final Polish and Quality Check
    result = finalPolishAndQualityCheck(result, maintainTone, documentAnalysis);

    console.log('Advanced humanization completed');
    return result;
}

/**
 * Processes formatted documents while preserving structure
 */
function processFormattedDocument(text, aggressiveness, hesitationFrequency, documentAnalysis) {
    const lines = text.split('\n');
    const processedLines = [];

    for (let i = 0; i < lines.length; i++) {
        const line = lines[i];
        const trimmedLine = line.trim();

        // Preserve empty lines exactly as they are
        if (!trimmedLine) {
            processedLines.push(line); // This preserves the original whitespace/empty line
            continue;
        }

        // Check if this line should be protected
        if (shouldProtectLine(trimmedLine, documentAnalysis)) {
            processedLines.push(line);
            continue;
        }

        // Process the line content while preserving indentation
        const indentation = line.match(/^\s*/)[0];
        let processedContent = trimmedLine;

        // Apply conservative modifications to non-protected lines
        if (trimmedLine.length > 20) {
            // Apply only safe modifications that don't break structure
            processedContent = applySafeModifications(processedContent, aggressiveness, hesitationFrequency);
        }

        processedLines.push(indentation + processedContent);
    }

    return processedLines.join('\n');
}

/**
 * Checks if a line should be protected from modification
 */
function shouldProtectLine(line, documentAnalysis) {
    // Protect headings, section numbers, and formal markers
    const protectionPatterns = [
        /^[IVX]+\./,                    // Roman numerals
        /^[A-Z]\./,                     // Letter markers
        /^\d+\./,                       // Number markers
        /^\d+\.\d+/,                    // Decimal numbering
        /^[A-Z][A-Z\s]+:?\s*$/,        // ALL CAPS headings
        /^(Introduction|Conclusion|Summary|Abstract|Background|Methodology|Results):/i,
        /^(Hook|Thesis|Topic|Executive Summary|Key Findings|Strategic Recommendations):/i,
        /^(Step|Phase|Part)\s+\d+/i,   // Step/Phase markers
        /^[-•*]\s/,                     // Bullet points
        /^[a-z]\)\s/,                   // Lettered lists
        /^\d+\)\s/,                     // Numbered lists with parentheses
        /:\s*$/,                        // Lines ending with colon
        /^-\s/                          // Dash lists
    ];

    return protectionPatterns.some(pattern => pattern.test(line));
}

/**
 * Applies safe modifications that don't break document structure
 */
function applySafeModifications(text, aggressiveness, hesitationFrequency) {
    let result = text;

    // Only apply very conservative word replacements
    const safeReplacements = {
        'utilize': 'use',
        'implement': 'put in place',
        'demonstrate': 'show',
        'facilitate': 'help',
        'optimize': 'improve'
    };

    Object.entries(safeReplacements).forEach(([formal, casual]) => {
        if (Math.random() < aggressiveness * 0.3) {
            const regex = new RegExp(`\\b${formal}\\b`, 'gi');
            result = result.replace(regex, casual);
        }
    });

    // Very rarely add hesitation (only for long sentences)
    if (text.length > 50 && Math.random() < hesitationFrequency * 0.5) {
        const hesitations = ['Actually, ', 'Well, '];
        const hesitation = hesitations[Math.floor(Math.random() * hesitations.length)];
        result = hesitation + result.charAt(0).toLowerCase() + result.slice(1);
    }

    return result;
}

/**
 * Analyzes text structure to inform humanization decisions
 */
function analyzeTextStructure(text) {
    const sentences = text.split(/(?<=[.!?])\s+/);
    const words = text.split(/\s+/);
    
    const analysis = {
        sentenceCount: sentences.length,
        averageSentenceLength: sentences.reduce((sum, s) => sum + s.length, 0) / sentences.length,
        wordCount: words.length,
        averageWordLength: words.reduce((sum, w) => sum + w.length, 0) / words.length,
        sentenceLengths: sentences.map(s => s.length),
        complexSentenceRatio: sentences.filter(s => s.includes(',') || s.includes(';')).length / sentences.length,
        formalWordCount: countFormalWords(text),
        aiTriggerCount: countAITriggers(text)
    };
    
    // Calculate burstiness (variation in sentence length)
    const lengths = analysis.sentenceLengths;
    const mean = lengths.reduce((a, b) => a + b, 0) / lengths.length;
    const variance = lengths.reduce((sum, len) => sum + Math.pow(len - mean, 2), 0) / lengths.length;
    analysis.burstiness = Math.sqrt(variance) / mean;
    
    return analysis;
}

/**
 * Advanced sentence restructuring - the most critical component
 */
function advancedSentenceRestructuring(text, analysis, aggressiveness, protectedSentences = []) {
    let result = text;
    const sentences = result.split(/(?<=[.!?])\s+/);

    const restructuredSentences = sentences.map((sentence, index) => {
        // Skip protected sentences (headings, formal content, etc.)
        if (protectedSentences.includes(index)) {
            return sentence;
        }

        // Skip very short sentences
        if (sentence.length < 20) return sentence;

        let newSentence = sentence;

        // 1. Clause reordering (high impact on AI detection)
        if (Math.random() < aggressiveness * 0.4) {
            newSentence = reorderClauses(newSentence);
        }

        // 2. Sentence splitting/combining based on human patterns
        if (Math.random() < aggressiveness * 0.3) {
            if (sentence.length > 80 && sentence.includes(',')) {
                // Split long sentences
                const splitResult = splitSentenceNaturally(newSentence);
                if (splitResult.length > 1) {
                    return splitResult.join(' ');
                }
            }
        }

        // 3. Passive/Active voice variation (humans mix both)
        if (Math.random() < aggressiveness * 0.25) {
            newSentence = varyVoiceNaturally(newSentence);
        }

        // 4. Sentence starter variation
        if (Math.random() < aggressiveness * 0.35) {
            newSentence = varyStarterPhrases(newSentence);
        }

        return newSentence;
    });

    return restructuredSentences.join(' ');
}

/**
 * Reorders clauses within sentences to create more natural flow
 */
function reorderClauses(sentence) {
    // Handle sentences with dependent clauses
    const patterns = [
        // "Because X, Y" -> "Y because X"
        {
            regex: /^(Because|Since|As|When|While|If|Although|Though)\s+([^,]+),\s*(.+)$/i,
            reorder: (match, connector, clause1, clause2) => {
                if (Math.random() > 0.5) {
                    return `${clause2}, ${connector.toLowerCase()} ${clause1}`;
                }
                return match;
            }
        },
        // "X, which Y, Z" -> "X Z, which Y" or other variations
        {
            regex: /^([^,]+),\s*which\s+([^,]+),\s*(.+)$/i,
            reorder: (match, main, which, rest) => {
                const variations = [
                    `${main} ${rest}, which ${which}`,
                    `${main}, and it ${which}, ${rest}`,
                    match // Keep original sometimes
                ];
                return variations[Math.floor(Math.random() * variations.length)];
            }
        }
    ];
    
    for (const pattern of patterns) {
        const match = sentence.match(pattern.regex);
        if (match) {
            return pattern.reorder(...match);
        }
    }
    
    return sentence;
}

/**
 * Splits long sentences naturally at appropriate points
 */
function splitSentenceNaturally(sentence) {
    const splitPoints = [
        { regex: /,\s*(and|but|or|so)\s+/, replacement: '. ' },
        { regex: /;\s*/, replacement: '. ' },
        { regex: /,\s*which\s+/, replacement: '. This ' },
        { regex: /,\s*because\s+/, replacement: '. This is because ' }
    ];
    
    for (const point of splitPoints) {
        if (sentence.match(point.regex)) {
            const parts = sentence.split(point.regex);
            if (parts.length >= 2 && parts[0].length > 30 && parts[1].length > 20) {
                const firstPart = parts[0].trim() + '.';
                const secondPart = parts.slice(1).join(' ').trim();
                const capitalizedSecond = secondPart.charAt(0).toUpperCase() + secondPart.slice(1);
                return [firstPart, capitalizedSecond];
            }
        }
    }
    
    return [sentence];
}

/**
 * Varies voice (active/passive) naturally
 */
function varyVoiceNaturally(sentence) {
    // Simple passive to active conversion
    const passivePattern = /(\w+)\s+(?:is|was|are|were)\s+(\w+ed)\s+by\s+(\w+)/i;
    const passiveMatch = sentence.match(passivePattern);
    
    if (passiveMatch && Math.random() > 0.6) {
        const [full, object, verb, subject] = passiveMatch;
        const activeForm = verb.replace(/ed$/, ''); // Simplified
        return sentence.replace(full, `${subject} ${activeForm} ${object}`);
    }
    
    return sentence;
}

/**
 * Varies sentence starters to break AI patterns
 */
function varyStarterPhrases(sentence) {
    const starters = [
        { pattern: /^However,\s*/i, replacements: ['But ', 'Still, ', 'Yet '] },
        { pattern: /^Therefore,\s*/i, replacements: ['So ', 'Thus, ', 'Hence '] },
        { pattern: /^Furthermore,\s*/i, replacements: ['Also, ', 'Plus, ', 'And '] },
        { pattern: /^Moreover,\s*/i, replacements: ['Also, ', 'Plus, ', 'What\'s more, '] },
        { pattern: /^Additionally,\s*/i, replacements: ['Also, ', 'Plus, ', 'On top of that, '] },
        { pattern: /^In addition,\s*/i, replacements: ['Also, ', 'Plus, ', 'Besides, '] }
    ];
    
    for (const starter of starters) {
        if (sentence.match(starter.pattern)) {
            const replacement = starter.replacements[Math.floor(Math.random() * starter.replacements.length)];
            return sentence.replace(starter.pattern, replacement);
        }
    }
    
    return sentence;
}

/**
 * Context-aware synonym replacement using semantic understanding
 */
function contextAwareSynonymReplacement(text, aggressiveness, protectedSentences = []) {
    let result = text;
    const sentences = result.split(/(?<=[.!?])\s+/);
    
    // Advanced synonym groups with context awareness
    const contextualSynonyms = {
        // Verbs - context matters greatly
        'utilize': { 
            formal: ['employ', 'apply'], 
            casual: ['use', 'try'], 
            context: 'tool|method|approach' 
        },
        'demonstrate': { 
            formal: ['illustrate', 'exhibit'], 
            casual: ['show', 'prove'], 
            context: 'example|evidence|proof' 
        },
        'implement': { 
            formal: ['execute', 'deploy'], 
            casual: ['do', 'put in place', 'set up'], 
            context: 'plan|strategy|solution' 
        },
        // Adjectives - tone-sensitive
        'significant': { 
            formal: ['substantial', 'considerable'], 
            casual: ['big', 'major', 'important'], 
            context: 'impact|change|difference' 
        },
        'comprehensive': { 
            formal: ['thorough', 'extensive'], 
            casual: ['complete', 'full', 'detailed'], 
            context: 'analysis|study|review' 
        }
    };
    
    // Process each sentence, skipping protected ones
    const processedSentences = sentences.map((sentence, sentenceIndex) => {
        if (protectedSentences.includes(sentenceIndex)) {
            return sentence;
        }

        // Process each word with context
        const words = sentence.split(/(\s+)/);
        const processedWords = words.map((word, index) => {
            const cleanWord = word.toLowerCase().replace(/[^\w]/g, '');

            if (contextualSynonyms[cleanWord] && Math.random() < aggressiveness * 0.4) {
                const synonymGroup = contextualSynonyms[cleanWord];
                const context = getWordContext(words, index, 3);

                // Choose synonym based on context
                let synonyms;
                if (synonymGroup.context && context.includes(synonymGroup.context)) {
                    synonyms = [...synonymGroup.formal, ...synonymGroup.casual];
                } else if (context.includes('formal') || context.includes('academic')) {
                    synonyms = synonymGroup.formal;
                } else {
                    synonyms = synonymGroup.casual;
                }

                if (synonyms.length > 0) {
                    const chosen = synonyms[Math.floor(Math.random() * synonyms.length)];
                    return preserveCase(word, chosen);
                }
            }

            return word;
        });

        return processedWords.join('');
    });

    return processedSentences.join(' ');
}

/**
 * Gets context around a word for better synonym selection
 */
function getWordContext(words, index, radius) {
    const start = Math.max(0, index - radius);
    const end = Math.min(words.length, index + radius + 1);
    return words.slice(start, end).join(' ').toLowerCase();
}

/**
 * Preserves original word casing when replacing
 */
function preserveCase(original, replacement) {
    if (!original || !replacement) return replacement;
    
    if (original === original.toUpperCase()) {
        return replacement.toUpperCase();
    }
    if (original === original.toLowerCase()) {
        return replacement.toLowerCase();
    }
    if (original[0] === original[0].toUpperCase()) {
        return replacement.charAt(0).toUpperCase() + replacement.slice(1).toLowerCase();
    }
    return replacement;
}

/**
 * Counts formal words that AI tends to overuse
 */
function countFormalWords(text) {
    const formalWords = [
        'utilize', 'implement', 'demonstrate', 'facilitate', 'optimize',
        'comprehensive', 'significant', 'substantial', 'furthermore', 'moreover'
    ];
    
    let count = 0;
    const lowerText = text.toLowerCase();
    formalWords.forEach(word => {
        const regex = new RegExp(`\\b${word}\\b`, 'g');
        const matches = lowerText.match(regex);
        if (matches) count += matches.length;
    });
    
    return count;
}

/**
 * Counts AI trigger phrases
 */
function countAITriggers(text) {
    const triggers = [
        'it is important to note', 'it should be noted', 'in conclusion',
        'to summarize', 'furthermore', 'moreover', 'additionally'
    ];

    let count = 0;
    const lowerText = text.toLowerCase();
    triggers.forEach(trigger => {
        if (lowerText.includes(trigger)) count++;
    });

    return count;
}

/**
 * Enhances perplexity and burstiness - critical for bypassing AI detection
 */
function enhancePerplexityAndBurstiness(text, analysis, aggressiveness, protectedSentences = []) {
    let result = text;
    const sentences = result.split(/(?<=[.!?])\s+/);

    // Calculate target burstiness (humans have higher variation)
    const targetBurstiness = Math.max(0.4, analysis.burstiness * 1.5);

    const enhancedSentences = sentences.map((sentence, index) => {
        // Skip protected sentences
        if (protectedSentences.includes(index)) {
            return sentence;
        }

        let newSentence = sentence;

        // 1. Vary sentence complexity dramatically
        if (Math.random() < aggressiveness * 0.5) {
            if (sentence.length > 60) {
                // Make some sentences much simpler
                newSentence = simplifyComplexSentence(newSentence);
            } else if (sentence.length < 30) {
                // Make some sentences more complex
                newSentence = complexifySimpleSentence(newSentence);
            }
        }

        // 2. Add unexpected word choices (increase perplexity)
        if (Math.random() < aggressiveness * 0.3) {
            newSentence = addUnexpectedWordChoices(newSentence);
        }

        // 3. Inject human-like redundancy and clarification
        if (Math.random() < aggressiveness * 0.2) {
            newSentence = addHumanRedundancy(newSentence);
        }

        return newSentence;
    });

    return enhancedSentences.join(' ');
}

/**
 * Simplifies complex sentences to increase burstiness
 */
function simplifyComplexSentence(sentence) {
    // Remove unnecessary qualifiers
    let simplified = sentence
        .replace(/\b(very|quite|rather|somewhat|fairly|pretty)\s+/gi, '')
        .replace(/\b(in order to|so as to)\b/gi, 'to')
        .replace(/\b(due to the fact that|owing to the fact that)\b/gi, 'because')
        .replace(/\b(in spite of the fact that|despite the fact that)\b/gi, 'although');

    // Break at natural points
    if (simplified.includes(' and ') && simplified.length > 80) {
        const parts = simplified.split(' and ');
        if (parts.length === 2 && parts[0].length > 20 && parts[1].length > 20) {
            return parts[0].trim() + '. ' + parts[1].charAt(0).toUpperCase() + parts[1].slice(1);
        }
    }

    return simplified;
}

/**
 * Adds complexity to simple sentences
 */
function complexifySimpleSentence(sentence) {
    const complexifiers = [
        { pattern: /^(\w+\s+\w+)\s+(\w+)/, replacement: '$1 actually $2' },
        { pattern: /(\w+)\s+(is|are|was|were)\s+(\w+)/, replacement: '$1 $2 really $3' },
        { pattern: /\.$/, replacement: ', which is interesting.' }
    ];

    for (const complexifier of complexifiers) {
        if (sentence.match(complexifier.pattern) && Math.random() > 0.7) {
            return sentence.replace(complexifier.pattern, complexifier.replacement);
        }
    }

    return sentence;
}

/**
 * Adds unexpected word choices to increase perplexity
 */
function addUnexpectedWordChoices(sentence) {
    const unexpectedReplacements = {
        'good': ['solid', 'decent', 'fine', 'okay'],
        'bad': ['rough', 'tough', 'tricky', 'messy'],
        'big': ['huge', 'massive', 'enormous', 'giant'],
        'small': ['tiny', 'little', 'mini', 'compact'],
        'fast': ['quick', 'speedy', 'rapid', 'swift'],
        'slow': ['sluggish', 'gradual', 'steady', 'leisurely']
    };

    let result = sentence;
    Object.entries(unexpectedReplacements).forEach(([common, alternatives]) => {
        const regex = new RegExp(`\\b${common}\\b`, 'gi');
        result = result.replace(regex, (match) => {
            if (Math.random() > 0.7) {
                const alternative = alternatives[Math.floor(Math.random() * alternatives.length)];
                return preserveCase(match, alternative);
            }
            return match;
        });
    });

    return result;
}

/**
 * Adds human-like redundancy and clarification
 */
function addHumanRedundancy(sentence) {
    const redundancyPatterns = [
        { pattern: /\b(important|significant|crucial)\b/gi, addition: ' (really $1)' },
        { pattern: /\b(easy|simple|straightforward)\b/gi, addition: ' (pretty $1)' },
        { pattern: /\b(difficult|hard|challenging)\b/gi, addition: ' (quite $1)' }
    ];

    for (const pattern of redundancyPatterns) {
        if (sentence.match(pattern.pattern) && Math.random() > 0.8) {
            return sentence.replace(pattern.pattern, (match) => {
                return match + pattern.addition.replace('$1', match.toLowerCase());
            });
        }
    }

    return sentence;
}

/**
 * Injects human writing patterns that AI rarely exhibits
 */
function injectHumanWritingPatterns(text, aggressiveness, hesitationFrequency, protectedSentences = []) {
    let result = text;

    // 1. Add human hesitation and self-correction (with controlled frequency)
    result = addHesitationPatterns(result, aggressiveness, hesitationFrequency, protectedSentences);

    // 2. Inject conversational elements (reduced frequency)
    result = addConversationalElements(result, aggressiveness * 0.5, protectedSentences);

    // 3. Add human-like tangents and asides (very reduced frequency)
    result = addHumanTangents(result, aggressiveness * 0.3, protectedSentences);

    // 4. Inject personal perspective markers (reduced frequency)
    result = addPersonalPerspective(result, aggressiveness * 0.4, protectedSentences);

    return result;
}

/**
 * Adds hesitation patterns that humans use - with smart frequency control
 */
function addHesitationPatterns(text, aggressiveness, hesitationFrequency, protectedSentences = []) {
    let result = text;
    const sentences = result.split(/(?<=[.!?])\s+/);

    const hesitationPatterns = [
        'Well, ',
        'So, ',
        'Now, ',
        'Actually, '
    ];

    // More aggressive hesitations (use sparingly)
    const strongHesitationPatterns = [
        'Look, ',
        'Listen, ',
        'Okay, ',
        'Right, '
    ];

    const enhancedSentences = sentences.map((sentence, index) => {
        // Skip protected sentences (headings, formal content, etc.)
        if (protectedSentences.includes(index)) {
            return sentence;
        }

        // Skip sentences that look like formal content
        if (isInappropriateForHesitation(sentence)) {
            return sentence;
        }

        // Use calculated hesitation frequency (max 5% for normal content, much lower for formal)
        if (Math.random() < hesitationFrequency && sentence.length > 25) {
            // Choose hesitation pattern based on context
            const patterns = sentence.length > 50 ? hesitationPatterns : hesitationPatterns.slice(0, 2);
            const hesitation = patterns[Math.floor(Math.random() * patterns.length)];
            return hesitation + sentence.charAt(0).toLowerCase() + sentence.slice(1);
        }

        return sentence;
    });

    return enhancedSentences.join(' ');
}

/**
 * Checks if a sentence is inappropriate for hesitation markers
 */
function isInappropriateForHesitation(sentence) {
    const inappropriatePatterns = [
        /^[IVX]+\./,                    // Roman numerals
        /^[A-Z]\./,                     // Letter markers
        /^\d+\./,                       // Number markers
        /^(Introduction|Conclusion|Summary|Abstract|Background):/i,
        /^(Hook|Thesis|Topic):/i,
        /^[A-Z][A-Z\s]+:?$/,           // ALL CAPS
        /:\s*$/,                        // Ends with colon
        /^(Step|Phase|Part)\s+\d+/i,   // Step/Phase markers
        /\b(API|URL|HTTP|JSON|XML)\b/i // Technical terms
    ];

    return inappropriatePatterns.some(pattern => pattern.test(sentence.trim()));
}

/**
 * Adds conversational elements - with context awareness
 */
function addConversationalElements(text, aggressiveness, protectedSentences = []) {
    let result = text;
    const sentences = result.split(/(?<=[.!?])\s+/);

    const processedSentences = sentences.map((sentence, index) => {
        // Skip protected sentences
        if (protectedSentences.includes(index)) {
            return sentence;
        }

        // Skip inappropriate content
        if (isInappropriateForHesitation(sentence)) {
            return sentence;
        }

        let processedSentence = sentence;

        // Very conservative conversational markers (max 2% chance)
        if (Math.random() < aggressiveness * 0.02 && sentence.length > 30) {
            const conversationalMarkers = [
                { pattern: /\b(This|That)\s+(is|was)\s+/gi, replacements: ['$1 $2 basically ', '$1 $2 essentially '] }
            ];

            conversationalMarkers.forEach(marker => {
                processedSentence = processedSentence.replace(marker.pattern, (match, ...groups) => {
                    const replacement = marker.replacements[Math.floor(Math.random() * marker.replacements.length)];
                    return replacement.replace(/\$(\d+)/g, (_, num) => groups[parseInt(num) - 1] || '');
                });
            });
        }

        return processedSentence;
    });

    return processedSentences.join(' ');
}

/**
 * Adds human-like tangents and asides - very conservative
 */
function addHumanTangents(text, aggressiveness, protectedSentences = []) {
    let result = text;
    const sentences = result.split(/(?<=[.!?])\s+/);

    const tangentMarkers = [
        ' (by the way)',
        ' (worth noting)'
    ];

    const enhancedSentences = sentences.map((sentence, index) => {
        // Skip protected sentences
        if (protectedSentences.includes(index)) {
            return sentence;
        }

        // Skip inappropriate content
        if (isInappropriateForHesitation(sentence)) {
            return sentence;
        }

        // Very low frequency for tangents (max 1% chance)
        if (Math.random() < aggressiveness * 0.01 && sentence.length > 50) {
            const tangent = tangentMarkers[Math.floor(Math.random() * tangentMarkers.length)];
            const insertPoint = Math.floor(sentence.length * 0.6);
            return sentence.slice(0, insertPoint) + tangent + sentence.slice(insertPoint);
        }
        return sentence;
    });

    return enhancedSentences.join(' ');
}

/**
 * Adds personal perspective markers - conservative approach
 */
function addPersonalPerspective(text, aggressiveness, protectedSentences = []) {
    let result = text;

    const perspectiveMarkers = [
        'I think ',
        'It seems ',
        'Personally, '
    ];

    const sentences = result.split(/(?<=[.!?])\s+/);
    const enhancedSentences = sentences.map((sentence, index) => {
        // Skip protected sentences
        if (protectedSentences.includes(index)) {
            return sentence;
        }

        // Skip inappropriate content
        if (isInappropriateForHesitation(sentence)) {
            return sentence;
        }

        // Very conservative frequency (max 2% chance)
        if (Math.random() < aggressiveness * 0.02 && sentence.length > 30 && index > 0) {
            const marker = perspectiveMarkers[Math.floor(Math.random() * perspectiveMarkers.length)];
            return marker + sentence.charAt(0).toLowerCase() + sentence.slice(1);
        }
        return sentence;
    });

    return enhancedSentences.join(' ');
}

/**
 * Subtle coherence disruption - breaks AI's perfect logical flow
 */
function subtleCoherenceDisruption(text, aggressiveness, protectedSentences = []) {
    let result = text;

    // 1. Occasionally break logical transitions
    result = breakLogicalTransitions(result, aggressiveness, protectedSentences);

    // 2. Add human-like topic drift (very conservative)
    result = addTopicDrift(result, aggressiveness * 0.2, protectedSentences);

    // 3. Insert human-like contradictions and corrections (very conservative)
    result = addHumanContradictions(result, aggressiveness * 0.1, protectedSentences);

    return result;
}

/**
 * Breaks overly logical transitions
 */
function breakLogicalTransitions(text, aggressiveness, protectedSentences = []) {
    let result = text;

    const logicalConnectors = [
        { pattern: /\bTherefore,\s*/gi, replacements: ['So, ', 'Thus, '] },
        { pattern: /\bConsequently,\s*/gi, replacements: ['So, ', 'Then, '] },
        { pattern: /\bAs a result,\s*/gi, replacements: ['So, ', 'Because of this, '] }
    ];

    logicalConnectors.forEach(connector => {
        result = result.replace(connector.pattern, (match, offset) => {
            // Check if this match is in a protected sentence
            const beforeMatch = text.substring(0, offset);
            const sentenceIndex = (beforeMatch.match(/[.!?]/g) || []).length;

            if (protectedSentences.includes(sentenceIndex)) {
                return match;
            }

            if (Math.random() < aggressiveness * 0.2) { // Reduced frequency
                const replacement = connector.replacements[Math.floor(Math.random() * connector.replacements.length)];
                return replacement;
            }
            return match;
        });
    });

    return result;
}

/**
 * Adds subtle topic drift - very conservative
 */
function addTopicDrift(text, aggressiveness, protectedSentences = []) {
    let result = text;
    const sentences = result.split(/(?<=[.!?])\s+/);

    const driftMarkers = [
        'On a related note, '
    ];

    const enhancedSentences = sentences.map((sentence, index) => {
        // Skip protected sentences
        if (protectedSentences.includes(index)) {
            return sentence;
        }

        // Skip inappropriate content
        if (isInappropriateForHesitation(sentence)) {
            return sentence;
        }

        // Very low frequency for topic drift (max 0.5% chance)
        if (Math.random() < aggressiveness * 0.005 && index > 2 && sentence.length > 30) {
            const drift = driftMarkers[Math.floor(Math.random() * driftMarkers.length)];
            return drift + sentence.charAt(0).toLowerCase() + sentence.slice(1);
        }
        return sentence;
    });

    return enhancedSentences.join(' ');
}

/**
 * Adds human-like contradictions and corrections - very conservative
 */
function addHumanContradictions(text, aggressiveness, protectedSentences = []) {
    let result = text;

    const contradictionPatterns = [
        'Let me rephrase that - '
    ];

    const sentences = result.split(/(?<=[.!?])\s+/);
    const enhancedSentences = sentences.map((sentence, index) => {
        // Skip protected sentences
        if (protectedSentences.includes(index)) {
            return sentence;
        }

        // Skip inappropriate content
        if (isInappropriateForHesitation(sentence)) {
            return sentence;
        }

        // Extremely low frequency for contradictions (max 0.2% chance)
        if (Math.random() < aggressiveness * 0.002 && index > 1 && sentence.length > 40) {
            const contradiction = contradictionPatterns[Math.floor(Math.random() * contradictionPatterns.length)];
            return contradiction + sentence.charAt(0).toLowerCase() + sentence.slice(1);
        }
        return sentence;
    });

    return enhancedSentences.join(' ');
}

/**
 * Final polish and quality check
 */
function finalPolishAndQualityCheck(text, maintainTone, documentAnalysis) {
    let result = text;

    // 1. Clean up any formatting issues (preserve structure for formal docs)
    result = cleanupFormatting(result, documentAnalysis);

    // 2. Ensure readability is maintained
    result = ensureReadability(result);

    // 3. Final human touch additions (very conservative for formal docs)
    if (!documentAnalysis.hasFormalStructure) {
        result = addFinalHumanTouches(result);
    }

    // 4. Quality validation with enhanced checks
    const quality = validateQuality(result, documentAnalysis);
    if (!quality.isAcceptable) {
        console.warn('Quality issues detected:', quality.issues);
        // Apply conservative fixes if quality is poor
        result = applyConservativeFixes(result, documentAnalysis);
    }

    return result;
}

/**
 * Cleans up formatting issues while preserving document structure
 */
function cleanupFormatting(text, documentAnalysis) {
    if (documentAnalysis && documentAnalysis.preserveFormatting) {
        // For formal documents, preserve line breaks and only clean up basic issues
        return text
            .replace(/[ \t]+/g, ' ') // Multiple spaces/tabs to single space (but preserve newlines)
            .replace(/\s+([.!?])/g, '$1') // Remove space before punctuation
            .replace(/([.!?])\s*([.!?])/g, '$1 ') // Fix multiple punctuation
            .replace(/\s+,/g, ',') // Remove space before comma
            .replace(/,([^\s])/g, ', $1') // Add space after comma
            .replace(/^\s+|\s+$/gm, ''); // Trim each line but preserve line breaks
    } else {
        // For regular text, standard cleanup
        return text
            .replace(/\s+/g, ' ') // Multiple spaces to single
            .replace(/\s+([.!?])/g, '$1') // Remove space before punctuation
            .replace(/([.!?])\s*([.!?])/g, '$1 ') // Fix multiple punctuation
            .replace(/\s+,/g, ',') // Remove space before comma
            .replace(/,([^\s])/g, ', $1') // Add space after comma
            .trim();
    }
}

/**
 * Ensures readability is maintained
 */
function ensureReadability(text) {
    let result = text;

    // Check for overly long sentences and break them
    const sentences = result.split(/(?<=[.!?])\s+/);
    const readableSentences = sentences.map(sentence => {
        if (sentence.length > 150) {
            // Find a good breaking point
            const breakPoints = [', and ', ', but ', ', so ', '; '];
            for (const breakPoint of breakPoints) {
                const index = sentence.indexOf(breakPoint, 60);
                if (index > 0 && index < sentence.length - 30) {
                    const firstPart = sentence.substring(0, index).trim() + '.';
                    const secondPart = sentence.substring(index + breakPoint.length).trim();
                    return firstPart + ' ' + secondPart.charAt(0).toUpperCase() + secondPart.slice(1);
                }
            }
        }
        return sentence;
    });

    return readableSentences.join(' ');
}

/**
 * Adds final human touches
 */
function addFinalHumanTouches(text) {
    let result = text;

    // Add occasional emphasis
    result = result.replace(/\b(really|very|quite)\s+(\w+)/gi, (match, intensifier, word) => {
        if (Math.random() > 0.9) {
            return `${intensifier}, ${intensifier} ${word}`;
        }
        return match;
    });

    // Add occasional informal contractions
    const informalContractions = {
        'going to': "gonna",
        'want to': "wanna",
        'have to': "gotta",
        'out of': "outta"
    };

    Object.entries(informalContractions).forEach(([formal, informal]) => {
        const regex = new RegExp(`\\b${formal}\\b`, 'gi');
        result = result.replace(regex, (match) => {
            if (Math.random() > 0.95) { // Very rarely
                return preserveCase(match, informal);
            }
            return match;
        });
    });

    return result;
}

/**
 * Validates text quality with enhanced checks
 */
function validateQuality(text, documentAnalysis) {
    const issues = [];

    // Check for basic grammar issues
    if (text.includes('..')) issues.push('Multiple periods found');
    if (text.includes('  ') && !documentAnalysis?.preserveFormatting) issues.push('Multiple spaces found');
    if (text.match(/[a-z]\.[A-Z]/)) issues.push('Missing space after period');

    // Check for readability
    const sentences = text.split(/(?<=[.!?])\s+/);
    const avgLength = sentences.reduce((sum, s) => sum + s.length, 0) / sentences.length;
    if (avgLength > 120) issues.push('Average sentence length too high');

    // Check for excessive hesitation markers
    const hesitationMarkers = ['well,', 'so,', 'actually,', 'listen,', 'look,'];
    let hesitationCount = 0;
    hesitationMarkers.forEach(marker => {
        hesitationCount += (text.toLowerCase().match(new RegExp(`\\b${marker}`, 'g')) || []).length;
    });

    // More strict limits for hesitation markers
    const maxHesitationRatio = documentAnalysis?.hasFormalStructure ? 0.02 : 0.05; // 2% for formal, 5% for casual
    if (hesitationCount > sentences.length * maxHesitationRatio) {
        issues.push('Too many hesitation markers');
    }

    // Check for inappropriate hesitation in formal content
    if (documentAnalysis?.hasFormalStructure) {
        const inappropriateHesitations = text.match(/\b(listen|look),\s*[a-z]/gi);
        if (inappropriateHesitations && inappropriateHesitations.length > 0) {
            issues.push('Inappropriate hesitation markers in formal content');
        }
    }

    return {
        isAcceptable: issues.length === 0,
        issues: issues
    };
}

/**
 * Applies conservative fixes if quality is poor
 */
function applyConservativeFixes(text, documentAnalysis) {
    let result = text;

    // Remove excessive hesitation markers
    const excessiveHesitations = ['\\blisten,\\s*', '\\blook,\\s*', '\\bwell,\\s*', '\\bso,\\s*'];
    excessiveHesitations.forEach(pattern => {
        result = result.replace(new RegExp(pattern, 'gi'), '');
    });

    // Remove parenthetical remarks if too many
    const parentheticalCount = (result.match(/\([^)]*\)/g) || []).length;
    const sentences = result.split(/(?<=[.!?])\s+/);
    if (parentheticalCount > sentences.length * 0.1) {
        result = result.replace(/\s*\([^)]*\)\s*/g, ' ');
    }

    // Clean up spaces appropriately
    if (documentAnalysis?.preserveFormatting) {
        result = result.replace(/[ \t]+/g, ' '); // Preserve line breaks
    } else {
        result = result.replace(/\s+/g, ' ').trim(); // Standard cleanup
    }

    return result;
}
