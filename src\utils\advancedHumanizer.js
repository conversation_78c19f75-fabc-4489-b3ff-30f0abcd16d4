/**
 * Advanced Humanization Engine - Designed to achieve ≤20% AI detection
 * Implements sophisticated algorithms based on modern AI detection research
 */

/**
 * Main advanced humanization function
 * Applies multiple sophisticated techniques to bypass AI detection
 */
export function advancedHumanization(text, options = {}) {
    const {
        aggressiveness = 0.7, // 0-1 scale, higher = more changes
        preserveLength = false,
        maintainTone = true
    } = options;

    let result = text;
    
    console.log('Starting advanced humanization...');
    
    // Phase 1: Structural Analysis and Preparation
    const textAnalysis = analyzeTextStructure(result);
    
    // Phase 2: Advanced Sentence Restructuring (most important for detection bypass)
    result = advancedSentenceRestructuring(result, textAnalysis, aggressiveness);
    
    // Phase 3: Context-Aware Synonym Replacement
    result = contextAwareSynonymReplacement(result, aggressiveness);
    
    // Phase 4: Perplexity and Burstiness Enhancement
    result = enhancePerplexityAndBurstiness(result, textAnalysis, aggressiveness);
    
    // Phase 5: Human Writing Pattern Injection
    result = injectHumanWritingPatterns(result, aggressiveness);
    
    // Phase 6: Semantic Coherence Disruption (subtle)
    result = subtleCoherenceDisruption(result, aggressiveness);
    
    // Phase 7: Final Polish and Quality Check
    result = finalPolishAndQualityCheck(result, maintainTone);
    
    console.log('Advanced humanization completed');
    return result;
}

/**
 * Analyzes text structure to inform humanization decisions
 */
function analyzeTextStructure(text) {
    const sentences = text.split(/(?<=[.!?])\s+/);
    const words = text.split(/\s+/);
    
    const analysis = {
        sentenceCount: sentences.length,
        averageSentenceLength: sentences.reduce((sum, s) => sum + s.length, 0) / sentences.length,
        wordCount: words.length,
        averageWordLength: words.reduce((sum, w) => sum + w.length, 0) / words.length,
        sentenceLengths: sentences.map(s => s.length),
        complexSentenceRatio: sentences.filter(s => s.includes(',') || s.includes(';')).length / sentences.length,
        formalWordCount: countFormalWords(text),
        aiTriggerCount: countAITriggers(text)
    };
    
    // Calculate burstiness (variation in sentence length)
    const lengths = analysis.sentenceLengths;
    const mean = lengths.reduce((a, b) => a + b, 0) / lengths.length;
    const variance = lengths.reduce((sum, len) => sum + Math.pow(len - mean, 2), 0) / lengths.length;
    analysis.burstiness = Math.sqrt(variance) / mean;
    
    return analysis;
}

/**
 * Advanced sentence restructuring - the most critical component
 */
function advancedSentenceRestructuring(text, analysis, aggressiveness) {
    let result = text;
    const sentences = result.split(/(?<=[.!?])\s+/);
    
    const restructuredSentences = sentences.map((sentence, index) => {
        // Skip very short sentences
        if (sentence.length < 20) return sentence;
        
        let newSentence = sentence;
        
        // 1. Clause reordering (high impact on AI detection)
        if (Math.random() < aggressiveness * 0.4) {
            newSentence = reorderClauses(newSentence);
        }
        
        // 2. Sentence splitting/combining based on human patterns
        if (Math.random() < aggressiveness * 0.3) {
            if (sentence.length > 80 && sentence.includes(',')) {
                // Split long sentences
                const splitResult = splitSentenceNaturally(newSentence);
                if (splitResult.length > 1) {
                    return splitResult.join(' ');
                }
            }
        }
        
        // 3. Passive/Active voice variation (humans mix both)
        if (Math.random() < aggressiveness * 0.25) {
            newSentence = varyVoiceNaturally(newSentence);
        }
        
        // 4. Sentence starter variation
        if (Math.random() < aggressiveness * 0.35) {
            newSentence = varyStarterPhrases(newSentence);
        }
        
        return newSentence;
    });
    
    return restructuredSentences.join(' ');
}

/**
 * Reorders clauses within sentences to create more natural flow
 */
function reorderClauses(sentence) {
    // Handle sentences with dependent clauses
    const patterns = [
        // "Because X, Y" -> "Y because X"
        {
            regex: /^(Because|Since|As|When|While|If|Although|Though)\s+([^,]+),\s*(.+)$/i,
            reorder: (match, connector, clause1, clause2) => {
                if (Math.random() > 0.5) {
                    return `${clause2}, ${connector.toLowerCase()} ${clause1}`;
                }
                return match;
            }
        },
        // "X, which Y, Z" -> "X Z, which Y" or other variations
        {
            regex: /^([^,]+),\s*which\s+([^,]+),\s*(.+)$/i,
            reorder: (match, main, which, rest) => {
                const variations = [
                    `${main} ${rest}, which ${which}`,
                    `${main}, and it ${which}, ${rest}`,
                    match // Keep original sometimes
                ];
                return variations[Math.floor(Math.random() * variations.length)];
            }
        }
    ];
    
    for (const pattern of patterns) {
        const match = sentence.match(pattern.regex);
        if (match) {
            return pattern.reorder(...match);
        }
    }
    
    return sentence;
}

/**
 * Splits long sentences naturally at appropriate points
 */
function splitSentenceNaturally(sentence) {
    const splitPoints = [
        { regex: /,\s*(and|but|or|so)\s+/, replacement: '. ' },
        { regex: /;\s*/, replacement: '. ' },
        { regex: /,\s*which\s+/, replacement: '. This ' },
        { regex: /,\s*because\s+/, replacement: '. This is because ' }
    ];
    
    for (const point of splitPoints) {
        if (sentence.match(point.regex)) {
            const parts = sentence.split(point.regex);
            if (parts.length >= 2 && parts[0].length > 30 && parts[1].length > 20) {
                const firstPart = parts[0].trim() + '.';
                const secondPart = parts.slice(1).join(' ').trim();
                const capitalizedSecond = secondPart.charAt(0).toUpperCase() + secondPart.slice(1);
                return [firstPart, capitalizedSecond];
            }
        }
    }
    
    return [sentence];
}

/**
 * Varies voice (active/passive) naturally
 */
function varyVoiceNaturally(sentence) {
    // Simple passive to active conversion
    const passivePattern = /(\w+)\s+(?:is|was|are|were)\s+(\w+ed)\s+by\s+(\w+)/i;
    const passiveMatch = sentence.match(passivePattern);
    
    if (passiveMatch && Math.random() > 0.6) {
        const [full, object, verb, subject] = passiveMatch;
        const activeForm = verb.replace(/ed$/, ''); // Simplified
        return sentence.replace(full, `${subject} ${activeForm} ${object}`);
    }
    
    return sentence;
}

/**
 * Varies sentence starters to break AI patterns
 */
function varyStarterPhrases(sentence) {
    const starters = [
        { pattern: /^However,\s*/i, replacements: ['But ', 'Still, ', 'Yet '] },
        { pattern: /^Therefore,\s*/i, replacements: ['So ', 'Thus, ', 'Hence '] },
        { pattern: /^Furthermore,\s*/i, replacements: ['Also, ', 'Plus, ', 'And '] },
        { pattern: /^Moreover,\s*/i, replacements: ['Also, ', 'Plus, ', 'What\'s more, '] },
        { pattern: /^Additionally,\s*/i, replacements: ['Also, ', 'Plus, ', 'On top of that, '] },
        { pattern: /^In addition,\s*/i, replacements: ['Also, ', 'Plus, ', 'Besides, '] }
    ];
    
    for (const starter of starters) {
        if (sentence.match(starter.pattern)) {
            const replacement = starter.replacements[Math.floor(Math.random() * starter.replacements.length)];
            return sentence.replace(starter.pattern, replacement);
        }
    }
    
    return sentence;
}

/**
 * Context-aware synonym replacement using semantic understanding
 */
function contextAwareSynonymReplacement(text, aggressiveness) {
    let result = text;
    
    // Advanced synonym groups with context awareness
    const contextualSynonyms = {
        // Verbs - context matters greatly
        'utilize': { 
            formal: ['employ', 'apply'], 
            casual: ['use', 'try'], 
            context: 'tool|method|approach' 
        },
        'demonstrate': { 
            formal: ['illustrate', 'exhibit'], 
            casual: ['show', 'prove'], 
            context: 'example|evidence|proof' 
        },
        'implement': { 
            formal: ['execute', 'deploy'], 
            casual: ['do', 'put in place', 'set up'], 
            context: 'plan|strategy|solution' 
        },
        // Adjectives - tone-sensitive
        'significant': { 
            formal: ['substantial', 'considerable'], 
            casual: ['big', 'major', 'important'], 
            context: 'impact|change|difference' 
        },
        'comprehensive': { 
            formal: ['thorough', 'extensive'], 
            casual: ['complete', 'full', 'detailed'], 
            context: 'analysis|study|review' 
        }
    };
    
    // Process each word with context
    const words = result.split(/(\s+)/);
    const processedWords = words.map((word, index) => {
        const cleanWord = word.toLowerCase().replace(/[^\w]/g, '');
        
        if (contextualSynonyms[cleanWord] && Math.random() < aggressiveness * 0.4) {
            const synonymGroup = contextualSynonyms[cleanWord];
            const context = getWordContext(words, index, 3);
            
            // Choose synonym based on context
            let synonyms;
            if (synonymGroup.context && context.includes(synonymGroup.context)) {
                synonyms = [...synonymGroup.formal, ...synonymGroup.casual];
            } else if (context.includes('formal') || context.includes('academic')) {
                synonyms = synonymGroup.formal;
            } else {
                synonyms = synonymGroup.casual;
            }
            
            if (synonyms.length > 0) {
                const chosen = synonyms[Math.floor(Math.random() * synonyms.length)];
                return preserveCase(word, chosen);
            }
        }
        
        return word;
    });
    
    return processedWords.join('');
}

/**
 * Gets context around a word for better synonym selection
 */
function getWordContext(words, index, radius) {
    const start = Math.max(0, index - radius);
    const end = Math.min(words.length, index + radius + 1);
    return words.slice(start, end).join(' ').toLowerCase();
}

/**
 * Preserves original word casing when replacing
 */
function preserveCase(original, replacement) {
    if (!original || !replacement) return replacement;
    
    if (original === original.toUpperCase()) {
        return replacement.toUpperCase();
    }
    if (original === original.toLowerCase()) {
        return replacement.toLowerCase();
    }
    if (original[0] === original[0].toUpperCase()) {
        return replacement.charAt(0).toUpperCase() + replacement.slice(1).toLowerCase();
    }
    return replacement;
}

/**
 * Counts formal words that AI tends to overuse
 */
function countFormalWords(text) {
    const formalWords = [
        'utilize', 'implement', 'demonstrate', 'facilitate', 'optimize',
        'comprehensive', 'significant', 'substantial', 'furthermore', 'moreover'
    ];
    
    let count = 0;
    const lowerText = text.toLowerCase();
    formalWords.forEach(word => {
        const regex = new RegExp(`\\b${word}\\b`, 'g');
        const matches = lowerText.match(regex);
        if (matches) count += matches.length;
    });
    
    return count;
}

/**
 * Counts AI trigger phrases
 */
function countAITriggers(text) {
    const triggers = [
        'it is important to note', 'it should be noted', 'in conclusion',
        'to summarize', 'furthermore', 'moreover', 'additionally'
    ];

    let count = 0;
    const lowerText = text.toLowerCase();
    triggers.forEach(trigger => {
        if (lowerText.includes(trigger)) count++;
    });

    return count;
}

/**
 * Enhances perplexity and burstiness - critical for bypassing AI detection
 */
function enhancePerplexityAndBurstiness(text, analysis, aggressiveness) {
    let result = text;
    const sentences = result.split(/(?<=[.!?])\s+/);

    // Calculate target burstiness (humans have higher variation)
    const targetBurstiness = Math.max(0.4, analysis.burstiness * 1.5);

    const enhancedSentences = sentences.map((sentence, index) => {
        let newSentence = sentence;

        // 1. Vary sentence complexity dramatically
        if (Math.random() < aggressiveness * 0.5) {
            if (sentence.length > 60) {
                // Make some sentences much simpler
                newSentence = simplifyComplexSentence(newSentence);
            } else if (sentence.length < 30) {
                // Make some sentences more complex
                newSentence = complexifySimpleSentence(newSentence);
            }
        }

        // 2. Add unexpected word choices (increase perplexity)
        if (Math.random() < aggressiveness * 0.3) {
            newSentence = addUnexpectedWordChoices(newSentence);
        }

        // 3. Inject human-like redundancy and clarification
        if (Math.random() < aggressiveness * 0.2) {
            newSentence = addHumanRedundancy(newSentence);
        }

        return newSentence;
    });

    return enhancedSentences.join(' ');
}

/**
 * Simplifies complex sentences to increase burstiness
 */
function simplifyComplexSentence(sentence) {
    // Remove unnecessary qualifiers
    let simplified = sentence
        .replace(/\b(very|quite|rather|somewhat|fairly|pretty)\s+/gi, '')
        .replace(/\b(in order to|so as to)\b/gi, 'to')
        .replace(/\b(due to the fact that|owing to the fact that)\b/gi, 'because')
        .replace(/\b(in spite of the fact that|despite the fact that)\b/gi, 'although');

    // Break at natural points
    if (simplified.includes(' and ') && simplified.length > 80) {
        const parts = simplified.split(' and ');
        if (parts.length === 2 && parts[0].length > 20 && parts[1].length > 20) {
            return parts[0].trim() + '. ' + parts[1].charAt(0).toUpperCase() + parts[1].slice(1);
        }
    }

    return simplified;
}

/**
 * Adds complexity to simple sentences
 */
function complexifySimpleSentence(sentence) {
    const complexifiers = [
        { pattern: /^(\w+\s+\w+)\s+(\w+)/, replacement: '$1 actually $2' },
        { pattern: /(\w+)\s+(is|are|was|were)\s+(\w+)/, replacement: '$1 $2 really $3' },
        { pattern: /\.$/, replacement: ', which is interesting.' }
    ];

    for (const complexifier of complexifiers) {
        if (sentence.match(complexifier.pattern) && Math.random() > 0.7) {
            return sentence.replace(complexifier.pattern, complexifier.replacement);
        }
    }

    return sentence;
}

/**
 * Adds unexpected word choices to increase perplexity
 */
function addUnexpectedWordChoices(sentence) {
    const unexpectedReplacements = {
        'good': ['solid', 'decent', 'fine', 'okay'],
        'bad': ['rough', 'tough', 'tricky', 'messy'],
        'big': ['huge', 'massive', 'enormous', 'giant'],
        'small': ['tiny', 'little', 'mini', 'compact'],
        'fast': ['quick', 'speedy', 'rapid', 'swift'],
        'slow': ['sluggish', 'gradual', 'steady', 'leisurely']
    };

    let result = sentence;
    Object.entries(unexpectedReplacements).forEach(([common, alternatives]) => {
        const regex = new RegExp(`\\b${common}\\b`, 'gi');
        result = result.replace(regex, (match) => {
            if (Math.random() > 0.7) {
                const alternative = alternatives[Math.floor(Math.random() * alternatives.length)];
                return preserveCase(match, alternative);
            }
            return match;
        });
    });

    return result;
}

/**
 * Adds human-like redundancy and clarification
 */
function addHumanRedundancy(sentence) {
    const redundancyPatterns = [
        { pattern: /\b(important|significant|crucial)\b/gi, addition: ' (really $1)' },
        { pattern: /\b(easy|simple|straightforward)\b/gi, addition: ' (pretty $1)' },
        { pattern: /\b(difficult|hard|challenging)\b/gi, addition: ' (quite $1)' }
    ];

    for (const pattern of redundancyPatterns) {
        if (sentence.match(pattern.pattern) && Math.random() > 0.8) {
            return sentence.replace(pattern.pattern, (match) => {
                return match + pattern.addition.replace('$1', match.toLowerCase());
            });
        }
    }

    return sentence;
}

/**
 * Injects human writing patterns that AI rarely exhibits
 */
function injectHumanWritingPatterns(text, aggressiveness) {
    let result = text;

    // 1. Add human hesitation and self-correction
    result = addHesitationPatterns(result, aggressiveness);

    // 2. Inject conversational elements
    result = addConversationalElements(result, aggressiveness);

    // 3. Add human-like tangents and asides
    result = addHumanTangents(result, aggressiveness);

    // 4. Inject personal perspective markers
    result = addPersonalPerspective(result, aggressiveness);

    return result;
}

/**
 * Adds hesitation patterns that humans use
 */
function addHesitationPatterns(text, aggressiveness) {
    let result = text;
    const sentences = result.split(/(?<=[.!?])\s+/);

    const hesitationPatterns = [
        'Well, ',
        'So, ',
        'Now, ',
        'Look, ',
        'Listen, ',
        'Okay, ',
        'Right, ',
        'Actually, '
    ];

    const enhancedSentences = sentences.map((sentence, index) => {
        if (Math.random() < aggressiveness * 0.15 && sentence.length > 25) {
            const hesitation = hesitationPatterns[Math.floor(Math.random() * hesitationPatterns.length)];
            return hesitation + sentence.charAt(0).toLowerCase() + sentence.slice(1);
        }
        return sentence;
    });

    return enhancedSentences.join(' ');
}

/**
 * Adds conversational elements
 */
function addConversationalElements(text, aggressiveness) {
    let result = text;

    const conversationalMarkers = [
        { pattern: /\. ([A-Z])/g, replacements: ['. You know, $1', '. I mean, $1', '. Like, $1'] },
        { pattern: /\b(This|That)\s+(is|was)\s+/gi, replacements: ['$1 $2 basically ', '$1 $2 essentially ', '$1 $2 pretty much '] }
    ];

    conversationalMarkers.forEach(marker => {
        result = result.replace(marker.pattern, (match, ...groups) => {
            if (Math.random() < aggressiveness * 0.1) {
                const replacement = marker.replacements[Math.floor(Math.random() * marker.replacements.length)];
                return replacement.replace(/\$(\d+)/g, (_, num) => groups[parseInt(num) - 1] || '');
            }
            return match;
        });
    });

    return result;
}

/**
 * Adds human-like tangents and asides
 */
function addHumanTangents(text, aggressiveness) {
    let result = text;
    const sentences = result.split(/(?<=[.!?])\s+/);

    const tangentMarkers = [
        ' (by the way)',
        ' (incidentally)',
        ' (as an aside)',
        ' (just to mention)',
        ' (worth noting)',
        ' (interestingly enough)',
        ' (funny thing is)',
        ' (come to think of it)'
    ];

    const enhancedSentences = sentences.map((sentence, index) => {
        if (Math.random() < aggressiveness * 0.08 && sentence.length > 40) {
            const tangent = tangentMarkers[Math.floor(Math.random() * tangentMarkers.length)];
            const insertPoint = Math.floor(sentence.length * 0.6);
            return sentence.slice(0, insertPoint) + tangent + sentence.slice(insertPoint);
        }
        return sentence;
    });

    return enhancedSentences.join(' ');
}

/**
 * Adds personal perspective markers
 */
function addPersonalPerspective(text, aggressiveness) {
    let result = text;

    const perspectiveMarkers = [
        'In my experience, ',
        'From what I\'ve seen, ',
        'I think ',
        'It seems to me that ',
        'My take is that ',
        'The way I see it, ',
        'Personally, ',
        'If you ask me, '
    ];

    const sentences = result.split(/(?<=[.!?])\s+/);
    const enhancedSentences = sentences.map((sentence, index) => {
        if (Math.random() < aggressiveness * 0.12 && sentence.length > 30 && index > 0) {
            const marker = perspectiveMarkers[Math.floor(Math.random() * perspectiveMarkers.length)];
            return marker + sentence.charAt(0).toLowerCase() + sentence.slice(1);
        }
        return sentence;
    });

    return enhancedSentences.join(' ');
}

/**
 * Subtle coherence disruption - breaks AI's perfect logical flow
 */
function subtleCoherenceDisruption(text, aggressiveness) {
    let result = text;

    // 1. Occasionally break logical transitions
    result = breakLogicalTransitions(result, aggressiveness);

    // 2. Add human-like topic drift
    result = addTopicDrift(result, aggressiveness);

    // 3. Insert human-like contradictions and corrections
    result = addHumanContradictions(result, aggressiveness);

    return result;
}

/**
 * Breaks overly logical transitions
 */
function breakLogicalTransitions(text, aggressiveness) {
    let result = text;

    const logicalConnectors = [
        { pattern: /\bTherefore,\s*/gi, replacements: ['So, ', 'Anyway, ', 'Well, '] },
        { pattern: /\bConsequently,\s*/gi, replacements: ['So, ', 'Then, ', 'Next, '] },
        { pattern: /\bAs a result,\s*/gi, replacements: ['So, ', 'Then, ', 'Because of this, '] }
    ];

    logicalConnectors.forEach(connector => {
        result = result.replace(connector.pattern, (match) => {
            if (Math.random() < aggressiveness * 0.3) {
                const replacement = connector.replacements[Math.floor(Math.random() * connector.replacements.length)];
                return replacement;
            }
            return match;
        });
    });

    return result;
}

/**
 * Adds subtle topic drift
 */
function addTopicDrift(text, aggressiveness) {
    let result = text;
    const sentences = result.split(/(?<=[.!?])\s+/);

    const driftMarkers = [
        'Speaking of which, ',
        'That reminds me, ',
        'On a related note, ',
        'While we\'re on the topic, ',
        'This brings up another point - '
    ];

    const enhancedSentences = sentences.map((sentence, index) => {
        if (Math.random() < aggressiveness * 0.05 && index > 2 && sentence.length > 25) {
            const drift = driftMarkers[Math.floor(Math.random() * driftMarkers.length)];
            return drift + sentence.charAt(0).toLowerCase() + sentence.slice(1);
        }
        return sentence;
    });

    return enhancedSentences.join(' ');
}

/**
 * Adds human-like contradictions and corrections
 */
function addHumanContradictions(text, aggressiveness) {
    let result = text;

    const contradictionPatterns = [
        'Actually, let me rephrase that - ',
        'Wait, that\'s not quite right - ',
        'Or maybe I should put it this way - ',
        'Actually, scratch that - ',
        'Let me think about this differently - '
    ];

    const sentences = result.split(/(?<=[.!?])\s+/);
    const enhancedSentences = sentences.map((sentence, index) => {
        if (Math.random() < aggressiveness * 0.03 && index > 1 && sentence.length > 30) {
            const contradiction = contradictionPatterns[Math.floor(Math.random() * contradictionPatterns.length)];
            return contradiction + sentence.charAt(0).toLowerCase() + sentence.slice(1);
        }
        return sentence;
    });

    return enhancedSentences.join(' ');
}

/**
 * Final polish and quality check
 */
function finalPolishAndQualityCheck(text, maintainTone) {
    let result = text;

    // 1. Clean up any formatting issues
    result = cleanupFormatting(result);

    // 2. Ensure readability is maintained
    result = ensureReadability(result);

    // 3. Final human touch additions
    result = addFinalHumanTouches(result);

    // 4. Quality validation
    const quality = validateQuality(result);
    if (!quality.isAcceptable) {
        console.warn('Quality issues detected:', quality.issues);
        // Apply conservative fixes if quality is poor
        result = applyConservativeFixes(result);
    }

    return result;
}

/**
 * Cleans up formatting issues
 */
function cleanupFormatting(text) {
    return text
        .replace(/\s+/g, ' ') // Multiple spaces to single
        .replace(/\s+([.!?])/g, '$1') // Remove space before punctuation
        .replace(/([.!?])\s*([.!?])/g, '$1 ') // Fix multiple punctuation
        .replace(/\s+,/g, ',') // Remove space before comma
        .replace(/,([^\s])/g, ', $1') // Add space after comma
        .trim();
}

/**
 * Ensures readability is maintained
 */
function ensureReadability(text) {
    let result = text;

    // Check for overly long sentences and break them
    const sentences = result.split(/(?<=[.!?])\s+/);
    const readableSentences = sentences.map(sentence => {
        if (sentence.length > 150) {
            // Find a good breaking point
            const breakPoints = [', and ', ', but ', ', so ', '; '];
            for (const breakPoint of breakPoints) {
                const index = sentence.indexOf(breakPoint, 60);
                if (index > 0 && index < sentence.length - 30) {
                    const firstPart = sentence.substring(0, index).trim() + '.';
                    const secondPart = sentence.substring(index + breakPoint.length).trim();
                    return firstPart + ' ' + secondPart.charAt(0).toUpperCase() + secondPart.slice(1);
                }
            }
        }
        return sentence;
    });

    return readableSentences.join(' ');
}

/**
 * Adds final human touches
 */
function addFinalHumanTouches(text) {
    let result = text;

    // Add occasional emphasis
    result = result.replace(/\b(really|very|quite)\s+(\w+)/gi, (match, intensifier, word) => {
        if (Math.random() > 0.9) {
            return `${intensifier}, ${intensifier} ${word}`;
        }
        return match;
    });

    // Add occasional informal contractions
    const informalContractions = {
        'going to': "gonna",
        'want to': "wanna",
        'have to': "gotta",
        'out of': "outta"
    };

    Object.entries(informalContractions).forEach(([formal, informal]) => {
        const regex = new RegExp(`\\b${formal}\\b`, 'gi');
        result = result.replace(regex, (match) => {
            if (Math.random() > 0.95) { // Very rarely
                return preserveCase(match, informal);
            }
            return match;
        });
    });

    return result;
}

/**
 * Validates text quality
 */
function validateQuality(text) {
    const issues = [];

    // Check for basic grammar issues
    if (text.includes('..')) issues.push('Multiple periods found');
    if (text.includes('  ')) issues.push('Multiple spaces found');
    if (text.match(/[a-z]\.[A-Z]/)) issues.push('Missing space after period');

    // Check for readability
    const sentences = text.split(/(?<=[.!?])\s+/);
    const avgLength = sentences.reduce((sum, s) => sum + s.length, 0) / sentences.length;
    if (avgLength > 120) issues.push('Average sentence length too high');

    // Check for too many modifications
    const modificationMarkers = ['\\(', 'actually', 'well,', 'so,', 'like,'];
    let modCount = 0;
    modificationMarkers.forEach(marker => {
        modCount += (text.toLowerCase().match(new RegExp(marker, 'g')) || []).length;
    });

    if (modCount > sentences.length * 0.3) issues.push('Too many modifications');

    return {
        isAcceptable: issues.length === 0,
        issues: issues
    };
}

/**
 * Applies conservative fixes if quality is poor
 */
function applyConservativeFixes(text) {
    let result = text;

    // Remove excessive modifications
    result = result.replace(/\s*\([^)]*\)\s*/g, ' '); // Remove parenthetical remarks
    result = result.replace(/\b(well|so|like),\s*/gi, ''); // Remove excessive hesitation
    result = result.replace(/\s+/g, ' ').trim(); // Clean up spaces

    return result;
}
