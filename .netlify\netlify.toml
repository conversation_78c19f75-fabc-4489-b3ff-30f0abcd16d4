functionsDirectory = "D:\\Project\\GhostLayer-2\\netlify\\functions"
functionsDirectoryOrigin = "config"
headersOrigin = "config"
redirectsOrigin = "config"

[build]
publish = "D:\\Project\\GhostLayer-2\\out"
publishOrigin = "config"
commandOrigin = "config"
command = "npm run build"
functions = "D:\\Project\\GhostLayer-2\\netlify\\functions"

[build.environment]
NODE_VERSION = "18"
NODE_ENV = "production"
NEXT_TELEMETRY_DISABLED = "1"
NETLIFY = "true"
NEXT_PUBLIC_SITE_URL = "https://ghostlayer.netlify.app"

[build.processing]
skip_processing = false

[build.processing.css]
bundle = true
minify = true

[build.processing.html]
pretty_urls = true

[build.processing.images]

[build.processing.js]
bundle = true
minify = true

[build.services]

[dev]
command = "npm run dev"
port = 3000.0
publish = "out"

[functions]

[functions."*"]
node_bundler = "esbuild"

[[plugins]]
origin = "default"
package = "@netlify/plugin-nextjs"

[plugins.inputs]

[[plugins]]
origin = "config"
package = "netlify-plugin-submit-sitemap"

[plugins.inputs]
baseUrl = "https://ghostlayer.netlify.app"
sitemapPath = "/sitemap.xml"
ignorePeriod = 0.0
providers = ["google", "bing", "yandex"]

[[headers]]
for = "/*"

[headers.values]
X-Frame-Options = "DENY"
X-Content-Type-Options = "nosniff"
Referrer-Policy = "strict-origin-when-cross-origin"
Permissions-Policy = "camera=(), microphone=(), geolocation=()"
X-DNS-Prefetch-Control = "on"
Strict-Transport-Security = "max-age=31536000; includeSubDomains"

[[headers]]
for = "/.netlify/functions/*"

[headers.values]
Access-Control-Allow-Origin = "*"
Access-Control-Allow-Methods = "GET, POST, PUT, DELETE, OPTIONS"
Access-Control-Allow-Headers = "Content-Type, Authorization, X-Requested-With"
Cache-Control = "no-store, max-age=0"

[[headers]]
for = "/_next/static/*"

[headers.values]
Cache-Control = "public, max-age=31536000, immutable"

[[headers]]
for = "/*.js"

[headers.values]
Cache-Control = "public, max-age=31536000, immutable"

[[headers]]
for = "/*.css"

[headers.values]
Cache-Control = "public, max-age=31536000, immutable"

[[headers]]
for = "/*.png"

[headers.values]
Cache-Control = "public, max-age=31536000, immutable"

[[headers]]
for = "/*.jpg"

[headers.values]
Cache-Control = "public, max-age=31536000, immutable"

[[headers]]
for = "/*.jpeg"

[headers.values]
Cache-Control = "public, max-age=31536000, immutable"

[[headers]]
for = "/*.gif"

[headers.values]
Cache-Control = "public, max-age=31536000, immutable"

[[headers]]
for = "/*.svg"

[headers.values]
Cache-Control = "public, max-age=31536000, immutable"

[[headers]]
for = "/*.ico"

[headers.values]
Cache-Control = "public, max-age=31536000, immutable"

[[headers]]
for = "/*.woff"

[headers.values]
Cache-Control = "public, max-age=31536000, immutable"

[[headers]]
for = "/*.woff2"

[headers.values]
Cache-Control = "public, max-age=31536000, immutable"

[[headers]]
for = "/*"

[headers.values]
X-Frame-Options = "DENY"
X-Content-Type-Options = "nosniff"
Referrer-Policy = "strict-origin-when-cross-origin"
Permissions-Policy = "camera=(), microphone=(), geolocation=()"

[[headers]]
for = "/api/*"

[headers.values]
Access-Control-Allow-Origin = "*"
Access-Control-Allow-Methods = "GET, POST, PUT, DELETE, OPTIONS"
Access-Control-Allow-Headers = "Content-Type, Authorization"
Cache-Control = "no-store, max-age=0"

[[headers]]
for = "/*.html"

[headers.values]
Cache-Control = "public, max-age=0, must-revalidate"

[[headers]]
for = "/static/*"

[headers.values]
Cache-Control = "public, max-age=31536000, immutable"

[[redirects]]
from = "/api/process-text"
to = "/.netlify/functions/process-text"
status = 200.0
force = false

[redirects.query]

[redirects.conditions]

[redirects.headers]

[[redirects]]
from = "/api/auth/callback/google"
to = "/.netlify/functions/auth-callback-google"
status = 200.0
force = false

[redirects.query]

[redirects.conditions]

[redirects.headers]

[[redirects]]
from = "/api/stripe/webhook"
to = "/.netlify/functions/stripe-webhook"
status = 200.0
force = false

[redirects.query]

[redirects.conditions]

[redirects.headers]

[[redirects]]
from = "/api/user/profile"
to = "/.netlify/functions/user-profile"
status = 200.0
force = false

[redirects.query]

[redirects.conditions]

[redirects.headers]

[[redirects]]
from = "/api/stripe/create-checkout-session"
to = "/.netlify/functions/create-checkout-session"
status = 200.0
force = false

[redirects.query]

[redirects.conditions]

[redirects.headers]

[[redirects]]
from = "/api/health"
to = "/.netlify/functions/health"
status = 200.0
force = false

[redirects.query]

[redirects.conditions]

[redirects.headers]

[[redirects]]
from = "/humanize"
to = "/"
status = 301.0
force = false

[redirects.query]

[redirects.conditions]

[redirects.headers]

[[redirects]]
from = "/ai-text-humanizer"
to = "/"
status = 301.0
force = false

[redirects.query]

[redirects.conditions]

[redirects.headers]

[[redirects]]
from = "/bypass-ai-detection"
to = "/"
status = 301.0
force = false

[redirects.query]

[redirects.conditions]

[redirects.headers]

[[redirects]]
from = "/make-ai-undetectable"
to = "/"
status = 301.0
force = false

[redirects.query]

[redirects.conditions]

[redirects.headers]

[[redirects]]
from = "/ghost-layer"
to = "/"
status = 301.0
force = false

[redirects.query]

[redirects.conditions]

[redirects.headers]

[[redirects]]
from = "/ghostlayer-ai"
to = "/"
status = 301.0
force = false

[redirects.query]

[redirects.conditions]

[redirects.headers]

[[redirects]]
from = "/ai-humanizer"
to = "/"
status = 301.0
force = false

[redirects.query]

[redirects.conditions]

[redirects.headers]

[[redirects]]
from = "/text-humanizer"
to = "/"
status = 301.0
force = false

[redirects.query]

[redirects.conditions]

[redirects.headers]

[[redirects]]
from = "/chatgpt-humanizer"
to = "/"
status = 301.0
force = false

[redirects.query]

[redirects.conditions]

[redirects.headers]

[[redirects]]
from = "/gpt-humanizer"
to = "/"
status = 301.0
force = false

[redirects.query]

[redirects.conditions]

[redirects.headers]

[[redirects]]
from = "/vs-quillbot"
to = "/features/"
status = 301.0
force = false

[redirects.query]

[redirects.conditions]

[redirects.headers]

[[redirects]]
from = "/vs-paraphraser"
to = "/features/"
status = 301.0
force = false

[redirects.query]

[redirects.conditions]

[redirects.headers]

[[redirects]]
from = "/vs-spinbot"
to = "/features/"
status = 301.0
force = false

[redirects.query]

[redirects.conditions]

[redirects.headers]

[[redirects]]
from = "/quillbot-alternative"
to = "/features/"
status = 301.0
force = false

[redirects.query]

[redirects.conditions]

[redirects.headers]

[[redirects]]
from = "/paraphraser-alternative"
to = "/features/"
status = 301.0
force = false

[redirects.query]

[redirects.conditions]

[redirects.headers]

[[redirects]]
from = "/bypass-gptzero"
to = "/#gptzero-bypass"
status = 301.0
force = false

[redirects.query]

[redirects.conditions]

[redirects.headers]

[[redirects]]
from = "/bypass-turnitin"
to = "/#turnitin-bypass"
status = 301.0
force = false

[redirects.query]

[redirects.conditions]

[redirects.headers]

[[redirects]]
from = "/bypass-originality"
to = "/#originality-bypass"
status = 301.0
force = false

[redirects.query]

[redirects.conditions]

[redirects.headers]

[[redirects]]
from = "/bypass-winston"
to = "/#winston-bypass"
status = 301.0
force = false

[redirects.query]

[redirects.conditions]

[redirects.headers]

[[redirects]]
from = "/academic-writing"
to = "/#academic-use"
status = 301.0
force = false

[redirects.query]

[redirects.conditions]

[redirects.headers]

[[redirects]]
from = "/content-creation"
to = "/#content-creation"
status = 301.0
force = false

[redirects.query]

[redirects.conditions]

[redirects.headers]

[[redirects]]
from = "/business-writing"
to = "/#business-writing"
status = 301.0
force = false

[redirects.query]

[redirects.conditions]

[redirects.headers]

[[redirects]]
from = "/creative-writing"
to = "/#creative-writing"
status = 301.0
force = false

[redirects.query]

[redirects.conditions]

[redirects.headers]

[[redirects]]
from = "/help"
to = "/about/"
status = 301.0
force = false

[redirects.query]

[redirects.conditions]

[redirects.headers]

[[redirects]]
from = "/support"
to = "/about/"
status = 301.0
force = false

[redirects.query]

[redirects.conditions]

[redirects.headers]

[[redirects]]
from = "/contact"
to = "/about/"
status = 301.0
force = false

[redirects.query]

[redirects.conditions]

[redirects.headers]

[[redirects]]
from = "/faq"
to = "/#faq"
status = 301.0
force = false

[redirects.query]

[redirects.conditions]

[redirects.headers]

[[redirects]]
from = "/plans"
to = "/pricing/"
status = 301.0
force = false

[redirects.query]

[redirects.conditions]

[redirects.headers]

[[redirects]]
from = "/subscription"
to = "/pricing/"
status = 301.0
force = false

[redirects.query]

[redirects.conditions]

[redirects.headers]

[[redirects]]
from = "/upgrade"
to = "/pricing/"
status = 301.0
force = false

[redirects.query]

[redirects.conditions]

[redirects.headers]

[[redirects]]
from = "/premium"
to = "/pricing/"
status = 301.0
force = false

[redirects.query]

[redirects.conditions]

[redirects.headers]

[[redirects]]
from = "/blog"
to = "/about/"
status = 301.0
force = false

[redirects.query]

[redirects.conditions]

[redirects.headers]

[[redirects]]
from = "/resources"
to = "/features/"
status = 301.0
force = false

[redirects.query]

[redirects.conditions]

[redirects.headers]

[[redirects]]
from = "/guides"
to = "/features/"
status = 301.0
force = false

[redirects.query]

[redirects.conditions]

[redirects.headers]

[[redirects]]
from = "/tutorials"
to = "/#how-to-humanize"
status = 301.0
force = false

[redirects.query]

[redirects.conditions]

[redirects.headers]

[[redirects]]
from = "/twitter"
to = "https://twitter.com/ghostlayer_ai"
status = 301.0
force = false

[redirects.query]

[redirects.conditions]

[redirects.headers]

[[redirects]]
from = "/github"
to = "https://github.com/HectorTa1989/stealthwriter-ai"
status = 301.0
force = false

[redirects.query]

[redirects.conditions]

[redirects.headers]

[[redirects]]
from = "/discord"
to = "https://discord.gg/ghostlayer"
status = 301.0
force = false

[redirects.query]

[redirects.conditions]

[redirects.headers]

[[redirects]]
from = "/stealthwriter"
to = "/"
status = 301.0
force = false

[redirects.query]

[redirects.conditions]

[redirects.headers]

[[redirects]]
from = "/stealth-writer"
to = "/"
status = 301.0
force = false

[redirects.query]

[redirects.conditions]

[redirects.headers]

[[redirects]]
from = "/features"
to = "/features/"
status = 301.0
force = false

[redirects.query]

[redirects.conditions]

[redirects.headers]

[[redirects]]
from = "/about"
to = "/about/"
status = 301.0
force = false

[redirects.query]

[redirects.conditions]

[redirects.headers]

[[redirects]]
from = "/pricing"
to = "/pricing/"
status = 301.0
force = false

[redirects.query]

[redirects.conditions]

[redirects.headers]

[[redirects]]
from = "/how-to-humanize-ai-text"
to = "/#how-to-humanize"
status = 301.0
force = false

[redirects.query]

[redirects.conditions]

[redirects.headers]

[[redirects]]
from = "/how-to-bypass-ai-detection"
to = "/#bypass-detection"
status = 301.0
force = false

[redirects.query]

[redirects.conditions]

[redirects.headers]

[[redirects]]
from = "/make-chatgpt-undetectable"
to = "/#undetectable-ai"
status = 301.0
force = false

[redirects.query]

[redirects.conditions]

[redirects.headers]

[[redirects]]
from = "/free-ai-humanizer"
to = "/"
status = 301.0
force = false

[redirects.query]

[redirects.conditions]

[redirects.headers]

[[redirects]]
from = "/gptzero-bypass"
to = "/#gptzero-bypass"
status = 301.0
force = false

[redirects.query]

[redirects.conditions]

[redirects.headers]

[[redirects]]
from = "/turnitin-ai-detection"
to = "/#turnitin-bypass"
status = 301.0
force = false

[redirects.query]

[redirects.conditions]

[redirects.headers]

[[redirects]]
from = "/originality-ai-bypass"
to = "/#originality-bypass"
status = 301.0
force = false

[redirects.query]

[redirects.conditions]

[redirects.headers]

[[redirects]]
from = "/winston-ai-bypass"
to = "/#winston-bypass"
status = 301.0
force = false

[redirects.query]

[redirects.conditions]

[redirects.headers]

[[redirects]]
from = "/best-ai-humanizer"
to = "/features/"
status = 301.0
force = false

[redirects.query]

[redirects.conditions]

[redirects.headers]

[[redirects]]
from = "/ai-humanizer-comparison"
to = "/features/"
status = 301.0
force = false

[redirects.query]

[redirects.conditions]

[redirects.headers]

[[redirects]]
from = "/quillbot-vs-ghostlayer"
to = "/features/"
status = 301.0
force = false

[redirects.query]

[redirects.conditions]

[redirects.headers]

[[redirects]]
from = "/mobile"
to = "/"
status = 301.0
force = false

[redirects.query]

[redirects.conditions]

[redirects.headers]

[[redirects]]
from = "/app"
to = "/"
status = 301.0
force = false

[redirects.query]

[redirects.conditions]

[redirects.headers]

[[redirects]]
from = "/download"
to = "/"
status = 301.0
force = false

[redirects.query]

[redirects.conditions]

[redirects.headers]

[[redirects]]
from = "/api-docs"
to = "/about/"
status = 301.0
force = false

[redirects.query]

[redirects.conditions]

[redirects.headers]

[[redirects]]
from = "/developers"
to = "/about/"
status = 301.0
force = false

[redirects.query]

[redirects.conditions]

[redirects.headers]

[[redirects]]
from = "/index.html"
to = "/"
status = 301.0
force = false

[redirects.query]

[redirects.conditions]

[redirects.headers]

[[redirects]]
from = "/features.html"
to = "/features/"
status = 301.0
force = false

[redirects.query]

[redirects.conditions]

[redirects.headers]

[[redirects]]
from = "/about.html"
to = "/about/"
status = 301.0
force = false

[redirects.query]

[redirects.conditions]

[redirects.headers]

[[redirects]]
from = "/pricing.html"
to = "/pricing/"
status = 301.0
force = false

[redirects.query]

[redirects.conditions]

[redirects.headers]

[[redirects]]
from = "/gostlayer"
to = "/"
status = 301.0
force = false

[redirects.query]

[redirects.conditions]

[redirects.headers]

[[redirects]]
from = "/ghostlayar"
to = "/"
status = 301.0
force = false

[redirects.query]

[redirects.conditions]

[redirects.headers]

[[redirects]]
from = "/ghost-layar"
to = "/"
status = 301.0
force = false

[redirects.query]

[redirects.conditions]

[redirects.headers]

[[redirects]]
from = "https://www.ghostlayer.netlify.app/*"
to = "https://ghostlayer.netlify.app/:splat"
status = 301.0
force = true

[redirects.query]

[redirects.conditions]

[redirects.headers]

[[redirects]]
from = "http://ghostlayer.netlify.app/*"
to = "https://ghostlayer.netlify.app/:splat"
status = 301.0
force = true

[redirects.query]

[redirects.conditions]

[redirects.headers]

[[redirects]]
from = "/*"
to = "/index.html"
status = 200.0
force = false

[redirects.query]

[redirects.conditions]

[redirects.headers]

[[redirects]]
from = "/home"
to = "/"
status = 301.0
force = false

[redirects.query]

[redirects.conditions]

[redirects.headers]

[[redirects]]
from = "/health"
to = "/api/health"
status = 200.0
force = false

[redirects.query]

[redirects.conditions]

[redirects.headers]

[[redirects]]
from = "/api/*"
to = "/.netlify/functions/:splat"
status = 200.0
force = true

[redirects.query]

[redirects.conditions]

[redirects.headers]