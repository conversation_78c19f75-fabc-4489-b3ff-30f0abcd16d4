import React from 'react';
import styles from '../../styles/BenefitsSection.module.css';

const BenefitsSection = () => {
  const benefits = [
    {
      icon: "🎯",
      title: "95% AI Detection Bypass Success Rate",
      description: "Our advanced algorithms successfully bypass AI detection tools including GPTZero, Turnitin, Originality.ai, Copyleaks, and Winston AI in 95% of cases.",
      stats: "Tested on 10,000+ text samples"
    },
    {
      icon: "⚡",
      title: "Lightning-Fast Processing",
      description: "Transform your AI-generated text in under 5 seconds. Our optimized processing pipeline ensures you get results faster than any competitor.",
      stats: "Average processing time: 2.3 seconds"
    },
    {
      icon: "🧠",
      title: "Preserves Original Meaning",
      description: "Unlike basic paraphrasing tools, GhostLayer maintains the semantic integrity and context of your original content while making it undetectable.",
      stats: "98% meaning preservation rate"
    },
    {
      icon: "🔧",
      title: "Multiple Humanization Levels",
      description: "Choose from Conservative, Balanced, or Aggressive humanization modes to match your specific needs and risk tolerance.",
      stats: "3 customizable processing modes"
    },
    {
      icon: "📚",
      title: "Supports All AI Models",
      description: "Works with text from ChatGPT, GPT-4, <PERSON>, <PERSON>, Jasper, Copy.ai, and any other AI writing tool or language model.",
      stats: "Compatible with 50+ AI tools"
    },
    {
      icon: "💰",
      title: "Free Tier Available",
      description: "Start using GhostLayer immediately with our generous free tier. No credit card required, no hidden fees, no registration barriers.",
      stats: "10 free humanizations daily"
    }
  ];

  const useCases = [
    {
      category: "Content Creation",
      icon: "✍️",
      examples: [
        "Blog posts and articles",
        "Social media content",
        "Marketing copy and ads",
        "Product descriptions",
        "Email newsletters"
      ],
      benefit: "Create authentic-sounding content that engages readers without triggering AI detection"
    },
    {
      category: "Academic Writing",
      icon: "🎓",
      examples: [
        "Research paper drafts",
        "Essay outlines and ideas",
        "Literature reviews",
        "Study guides and summaries",
        "Thesis research notes"
      ],
      benefit: "Transform AI-assisted research into natural academic writing (always follow your institution's AI policies)"
    },
    {
      category: "Business Communication",
      icon: "💼",
      examples: [
        "Professional emails",
        "Business proposals",
        "Meeting summaries",
        "Report drafts",
        "Client communications"
      ],
      benefit: "Ensure your AI-enhanced business writing sounds professional and human"
    },
    {
      category: "Creative Writing",
      icon: "🎨",
      examples: [
        "Story outlines and plots",
        "Character descriptions",
        "Creative writing prompts",
        "Poetry and prose",
        "Screenplay ideas"
      ],
      benefit: "Transform AI-generated creative ideas into authentic, original-sounding content"
    }
  ];

  return (
    <section className={styles.benefitsSection} id="benefits">
      <div className={styles.container}>
        {/* Benefits Section */}
        <div className={styles.benefitsContainer}>
          <header className={styles.header}>
            <h2 className={styles.title}>
              Why 10,000+ Users Choose GhostLayer for AI Text Humanization
            </h2>
            <p className={styles.subtitle}>
              Discover the proven benefits that make GhostLayer the #1 choice for bypassing AI detection
            </p>
          </header>

          <div className={styles.benefitsGrid}>
            {benefits.map((benefit, index) => (
              <div key={index} className={styles.benefitCard}>
                <div className={styles.benefitIcon}>{benefit.icon}</div>
                <h3 className={styles.benefitTitle}>{benefit.title}</h3>
                <p className={styles.benefitDescription}>{benefit.description}</p>
                <div className={styles.benefitStats}>{benefit.stats}</div>
              </div>
            ))}
          </div>
        </div>

        {/* Use Cases Section */}
        <div className={styles.useCasesContainer}>
          <header className={styles.header}>
            <h2 className={styles.title}>
              Perfect for Every AI Text Humanization Need
            </h2>
            <p className={styles.subtitle}>
              See how professionals across industries use GhostLayer to create undetectable, human-like content
            </p>
          </header>

          <div className={styles.useCasesGrid}>
            {useCases.map((useCase, index) => (
              <div key={index} className={styles.useCaseCard}>
                <div className={styles.useCaseHeader}>
                  <span className={styles.useCaseIcon}>{useCase.icon}</span>
                  <h3 className={styles.useCaseTitle}>{useCase.category}</h3>
                </div>
                <ul className={styles.examplesList}>
                  {useCase.examples.map((example, exampleIndex) => (
                    <li key={exampleIndex} className={styles.example}>
                      <span className={styles.checkmark}>✓</span>
                      {example}
                    </li>
                  ))}
                </ul>
                <div className={styles.useCaseBenefit}>
                  <strong>Key Benefit:</strong> {useCase.benefit}
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Statistics Section */}
        <div className={styles.statsSection}>
          <h3 className={styles.statsTitle}>Proven Results You Can Trust</h3>
          <div className={styles.statsGrid}>
            <div className={styles.statItem}>
              <div className={styles.statNumber}>95%</div>
              <div className={styles.statLabel}>AI Detection Bypass Rate</div>
              <div className={styles.statDetail}>Tested against major AI detectors</div>
            </div>
            <div className={styles.statItem}>
              <div className={styles.statNumber}>10,000+</div>
              <div className={styles.statLabel}>Texts Successfully Humanized</div>
              <div className={styles.statDetail}>Trusted by users worldwide</div>
            </div>
            <div className={styles.statItem}>
              <div className={styles.statNumber}>1.3s</div>
              <div className={styles.statLabel}>Average Processing Time</div>
              <div className={styles.statDetail}>Fastest in the industry</div>
            </div>
            <div className={styles.statItem}>
              <div className={styles.statNumber}>98%</div>
              <div className={styles.statLabel}>Meaning Preservation</div>
              <div className={styles.statDetail}>Content quality maintained</div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default BenefitsSection;
