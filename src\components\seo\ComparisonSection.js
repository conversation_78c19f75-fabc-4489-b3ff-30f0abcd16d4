import React from 'react';
import styles from '../../styles/ComparisonSection.module.css';

const ComparisonSection = () => {
  const competitors = [
    {
      name: "<PERSON>Layer",
      logo: "👻",
      isUs: true,
      features: {
        "AI Detection Bypass Rate": "95%",
        "Processing Speed": "< 2 seconds",
        "Text Length Support": "Up to 3000 words",
        "AI Models Supported": "All major models",
        "Meaning Preservation": "Excellent",
        "Free Tier Available": "Yes",
        "Pricing": "Free / $9.99/mo",
        "Specialized for AI Detection": "Yes",
        "Real-time Processing": "Yes",
        "Multiple Humanization Levels": "Yes"
      }
    },
    {
      name: "QuillBot",
      logo: "🪶",
      isUs: false,
      features: {
        "AI Detection Bypass Rate": "60-70%",
        "Processing Speed": "5-10 seconds",
        "Text Length Support": "Up to 125 words (free)",
        "AI Models Supported": "General paraphrasing",
        "Meaning Preservation": "Good",
        "Free Tier Available": "Limited",
        "Pricing": "$4.95-19.95/mo",
        "Specialized for AI Detection": "No",
        "Real-time Processing": "No",
        "Multiple Humanization Levels": "Limited"
      }
    },
    {
      name: "Paraphraser.io",
      logo: "📝",
      isUs: false,
      features: {
        "AI Detection Bypass Rate": "50-65%",
        "Processing Speed": "3-8 seconds",
        "Text Length Support": "Up to 500 words",
        "AI Models Supported": "General paraphrasing",
        "Meaning Preservation": "Fair",
        "Free Tier Available": "Yes",
        "Pricing": "Free / $7/mo",
        "Specialized for AI Detection": "No",
        "Real-time Processing": "Yes",
        "Multiple Humanization Levels": "Basic"
      }
    },
    {
      name: "Spinbot",
      logo: "🔄",
      isUs: false,
      features: {
        "AI Detection Bypass Rate": "40-55%",
        "Processing Speed": "2-5 seconds",
        "Text Length Support": "Up to 10,000 characters",
        "AI Models Supported": "Basic spinning",
        "Meaning Preservation": "Poor",
        "Free Tier Available": "Yes",
        "Pricing": "Free / $10/mo",
        "Specialized for AI Detection": "No",
        "Real-time Processing": "Yes",
        "Multiple Humanization Levels": "No"
      }
    }
  ];

  const featureKeys = Object.keys(competitors[0].features);

  return (
    <section className={styles.comparisonSection} id="comparison">
      <div className={styles.container}>
        <header className={styles.header}>
          <h2 className={styles.title}>
            Why GhostLayer Outperforms Other AI Text Humanizers
          </h2>
          <p className={styles.subtitle}>
            Compare GhostLayer's superior AI detection bypass capabilities with popular alternatives
          </p>
        </header>

        <div className={styles.tableContainer}>
          <table className={styles.comparisonTable}>
            <thead>
              <tr>
                <th className={styles.featureHeader}>Features</th>
                {competitors.map((competitor, index) => (
                  <th 
                    key={index} 
                    className={`${styles.competitorHeader} ${competitor.isUs ? styles.ourColumn : ''}`}
                  >
                    <div className={styles.competitorInfo}>
                      <span className={styles.logo}>{competitor.logo}</span>
                      <span className={styles.name}>{competitor.name}</span>
                      {competitor.isUs && <span className={styles.badge}>Best Choice</span>}
                    </div>
                  </th>
                ))}
              </tr>
            </thead>
            <tbody>
              {featureKeys.map((feature, featureIndex) => (
                <tr key={featureIndex} className={styles.featureRow}>
                  <td className={styles.featureCell}>{feature}</td>
                  {competitors.map((competitor, compIndex) => (
                    <td 
                      key={compIndex} 
                      className={`${styles.valueCell} ${competitor.isUs ? styles.ourValue : ''}`}
                    >
                      {competitor.features[feature]}
                      {competitor.isUs && featureIndex === 0 && (
                        <span className={styles.highlight}>★ Highest</span>
                      )}
                    </td>
                  ))}
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        <div className={styles.stats}>
          <div className={styles.stat}>
            <div className={styles.statNumber}>95%</div>
            <div className={styles.statLabel}>Success Rate vs AI Detectors</div>
            <div className={styles.statNote}>Tested against GPTZero, Turnitin, Originality.ai</div>
          </div>
          <div className={styles.stat}>
            <div className={styles.statNumber}>10,000+</div>
            <div className={styles.statLabel}>Texts Successfully Humanized</div>
            <div className={styles.statNote}>Trusted by content creators worldwide</div>
          </div>
          <div className={styles.stat}>
            <div className={styles.statNumber}>2-5s</div>
            <div className={styles.statLabel}>Average Processing Time</div>
            <div className={styles.statNote}>Fastest AI text humanization available</div>
          </div>
        </div>

        <div className={styles.cta}>
          <h3 className={styles.ctaTitle}>Ready to Experience the Difference?</h3>
          <p className={styles.ctaText}>
            Join thousands who've switched to GhostLayer for reliable AI detection bypass
          </p>
          <button 
            className={styles.ctaButton}
            onClick={() => window.scrollTo({ top: 0, behavior: 'smooth' })}
          >
            Try GhostLayer Free Now
          </button>
        </div>
      </div>
    </section>
  );
};

export default ComparisonSection;
